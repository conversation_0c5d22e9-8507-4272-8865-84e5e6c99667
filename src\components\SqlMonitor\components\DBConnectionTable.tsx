import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Form, Modal, App, Drawer } from 'antd';
import React, { useCallback, useState, useRef } from 'react';

// 导入重构后的模块
import type { DBConnection, DBConnectionSearchParams } from '../types';
import { DEFAULT_PAGINATION } from '../constants';
import { TaskService } from '../services';
import { useDBConnectionData, useDrawer } from '../hooks';
import { useDBConnectionTable } from '../hooks/useDBConnectionTable';
import { useSelection } from '../hooks/useSelection';
import { tableStyles } from '../styles';

// 导入拆分的组件
import { createDBConnectionTableColumns } from './DBConnectionTableColumns';
import { DBConnectionQuickSearchForm } from './forms/DBConnectionSearchForm';
import { DBConnectionActionButtons } from './DBConnectionActionButtons';
import { DBConnectionTableComponent } from './DBConnectionTableComponent';
import DBConnectionBasicForm from './forms/DBConnectionBasicForm';

interface DBConnectionTableProps {
  contentHeight?: number;
}

/**
 * 数据库连接管理表格组件
 * 包含查询、新增、编辑、删除、分页等完整功能
 */
const DBConnectionTable: React.FC<DBConnectionTableProps> = ({ contentHeight }) => {
  const { message } = App.useApp();

  // 使用自定义hooks管理状态
  const {
    data,
    loading,
    total,
    pagination,
    loadData,
    refreshData,
    resetData,
    updateSearchParams,
    updatePagination,
  } = useDBConnectionData({ autoLoad: true });

  const { rowSelection, clearSelection, getSelectedCount, getSelectedRows } = useSelection(data, {
    crossPage: true,
    onSelectionChange: (selectedKeys: React.Key[], selectedRows: DBConnection[]) => {
      console.log('选择变化:', { selectedKeys, selectedRows });
    },
  });

  const { tableScrollY, filteredInfo, handleTableChange, getSortOrder, resetSortAndFilter } =
    useDBConnectionTable({ contentHeight });

  // 抽屉状态管理
  const {
    drawerState: editDrawer,
    showDrawer: showEditDrawer,
    hideDrawer: hideEditDrawer,
  } = useDrawer();

  // 表单和数据状态
  const [searchForm] = Form.useForm();
  const [editForm] = Form.useForm();
  const [currentRecord, setCurrentRecord] = useState<DBConnection | null>(null);

  // 数据加载引用
  const loadDataRef = useRef(loadData);
  loadDataRef.current = loadData;

  // 搜索表单提交处理
  const handleSearchFormSubmit = useCallback(
    (values: DBConnectionSearchParams) => {
      console.log('搜索参数:', values);
      updateSearchParams(values);
      updatePagination(1, pagination.page_size);
      loadDataRef.current({
        ...values,
        current: 1,
        page_size: pagination.page_size,
      });
    },
    [updateSearchParams, updatePagination, pagination.page_size]
  );

  // 重置搜索
  const handleReset = useCallback(() => {
    searchForm.resetFields();
    resetSortAndFilter();
    resetData();
    updatePagination(DEFAULT_PAGINATION.current, DEFAULT_PAGINATION.page_size);
    loadDataRef.current({
      current: DEFAULT_PAGINATION.current,
      page_size: DEFAULT_PAGINATION.page_size,
    });
  }, [searchForm, resetSortAndFilter, resetData, updatePagination]);

  // 编辑处理
  const handleEdit = useCallback(
    (record: DBConnection) => {
      console.log('编辑数据库连接:', record);
      setCurrentRecord(record);
      editForm.setFieldsValue(record);
      showEditDrawer({
        visible: true,
        title: '编辑数据库连接',
        width: '60%',
      });
    },
    [editForm, showEditDrawer]
  );

  // 删除处理
  const handleDelete = useCallback(
    async (id: number) => {
      try {
        console.log('删除数据库连接:', id);
        await TaskService.deleteDbConnection(id);
        message.success('删除成功');
        await refreshData();
        clearSelection();
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
    [message, refreshData, clearSelection]
  );

  // 批量删除处理
  const handleBatchDelete = useCallback(() => {
    const selectedRows = getSelectedRows();
    if (selectedRows.length === 0) {
      message.warning('请先选择要删除的数据');
      return;
    }

    Modal.confirm({
      title: '确认批量删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除选中的 ${selectedRows.length} 条数据库连接吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const deletePromises = selectedRows.map(row => TaskService.deleteDbConnection(row.id));
          await Promise.all(deletePromises);
          message.success(`成功删除 ${selectedRows.length} 条记录`);
          await refreshData();
          clearSelection();
        } catch (error) {
          console.error('批量删除失败:', error);
          message.error('批量删除失败');
        }
      },
    });
  }, [getSelectedRows, message, refreshData, clearSelection]);

  // 表单提交处理
  const handleFormSubmit = useCallback(
    async (values: any) => {
      try {
        console.log('提交数据库连接表单:', values);

        if (currentRecord) {
          // 编辑模式
          await TaskService.updateDbConnection(currentRecord.id, values);
          message.success('更新成功');
        } else {
          // 新增模式
          await TaskService.addDbConnection(values);
          message.success('新增成功');
        }

        hideEditDrawer();
        editForm.resetFields();
        setCurrentRecord(null);
        await refreshData();
      } catch (error) {
        console.error('提交失败:', error);
        message.error('提交失败');
      }
    },
    [currentRecord, message, hideEditDrawer, editForm, refreshData]
  );

  // 取消编辑
  const handleCancel = useCallback(() => {
    hideEditDrawer();
    editForm.resetFields();
    setCurrentRecord(null);
  }, [hideEditDrawer, editForm]);

  // 表格列定义
  const columns = createDBConnectionTableColumns({
    filteredInfo,
    getSortOrder,
    onEdit: handleEdit,
    onDelete: handleDelete,
  });

  return (
    <div className="h-full flex flex-col">
      {/* 主要内容区域  */}
      <div className={tableStyles.mainContainer}>
        {/* 快速搜索表单区域 */}
        <DBConnectionQuickSearchForm
          form={searchForm}
          onSubmit={handleSearchFormSubmit}
          onReset={handleReset}
        />

        {/* 操作按钮区域 */}
        <DBConnectionActionButtons
          selectedCount={getSelectedCount()}
          onAddConnection={() => {
            setCurrentRecord(null);
            editForm.resetFields();
            showEditDrawer({
              visible: true,
              title: '新增数据库连接',
              width: '60%',
            });
          }}
          onBatchDelete={handleBatchDelete}
          onClearSelection={clearSelection}
        />

        {/* 表格主体区域 */}
        <DBConnectionTableComponent
          columns={columns}
          data={data}
          loading={loading}
          total={total}
          pagination={pagination}
          rowSelection={rowSelection}
          tableScrollY={tableScrollY}
          onTableChange={handleTableChange}
          onPaginationChange={(page: number, pageSize: number) => {
            updatePagination(page, pageSize);
            loadDataRef.current({
              current: page,
              page_size: pageSize,
            });
          }}
        />
      </div>

      {/* 编辑抽屉 */}
      <Drawer
        title={editDrawer.title}
        width={editDrawer.width}
        open={editDrawer.visible}
        onClose={handleCancel}
        destroyOnClose
      >
        <DBConnectionBasicForm
          form={editForm}
          initialData={currentRecord || undefined}
          onSubmit={handleFormSubmit}
          onCancel={handleCancel}
          loading={loading}
        />
      </Drawer>
    </div>
  );
};

export default DBConnectionTable;
