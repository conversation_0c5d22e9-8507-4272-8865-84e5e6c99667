/**
 * 其他信息表格列配置组件
 * 定义其他信息表格的列结构和操作
 */

import { DeleteOutlined, EditOutlined, SearchOutlined } from '@ant-design/icons';
import type { TableColumnsType } from 'antd';
import { Button, Input, Popconfirm, Space, Tag } from 'antd';
import type { FilterDropdownProps, FilterValue } from 'antd/es/table/interface';
import React from 'react';

import type { OtherInfo } from '../types';
import { tableStyles } from '../styles';

/**
 * 其他信息表格列配置组件的属性接口
 */
interface OtherInfoTableColumnsProps {
  /** 表格筛选状态信息 */
  filteredInfo: Record<string, FilterValue | null>;
  /** 获取指定列的排序状态 */
  getSortOrder: (columnKey: string) => 'ascend' | 'descend' | null;
  /** 编辑其他信息的回调函数 */
  onEdit: (record: OtherInfo) => void;
  /** 删除其他信息的回调函数 */
  onDelete: (id: number) => void;
}

/**
 * 创建其他信息表格列配置
 */
export const createOtherInfoTableColumns = ({
  filteredInfo,
  getSortOrder,
  onEdit,
  onDelete,
}: OtherInfoTableColumnsProps): TableColumnsType<OtherInfo> => {
  return [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      sorter: true,
      sortOrder: getSortOrder('id'),
      fixed: 'left',
    },
    {
      title: '信息名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      sorter: true,
      sortOrder: getSortOrder('name'),
      fixed: 'left',
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: FilterDropdownProps) => (
        <div className="p-2">
          <Input
            placeholder="搜索信息名称"
            value={selectedKeys[0]}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => confirm()}
            className="w-48 mb-2 block"
          />
          <Space>
            <Button
              type="primary"
              onClick={() => confirm()}
              icon={<SearchOutlined />}
              size="small"
              className="w-20"
            >
              搜索
            </Button>
            <Button onClick={() => clearFilters && clearFilters()} size="small" className="w-20">
              重置
            </Button>
          </Space>
        </div>
      ),
      filterIcon: (filtered: boolean) => (
        <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
      ),
      filteredValue: filteredInfo.name || null,
    },
    {
      title: '业务系统',
      dataIndex: 'business',
      key: 'business',
      width: 180,
      sorter: true,
      sortOrder: getSortOrder('business'),
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: FilterDropdownProps) => (
        <div className="p-2">
          <Input
            placeholder="搜索业务系统"
            value={selectedKeys[0]}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => confirm()}
            className="w-48 mb-2 block"
          />
          <Space>
            <Button
              type="primary"
              onClick={() => confirm()}
              icon={<SearchOutlined />}
              size="small"
              className="w-20"
            >
              搜索
            </Button>
            <Button onClick={() => clearFilters && clearFilters()} size="small" className="w-20">
              重置
            </Button>
          </Space>
        </div>
      ),
      filterIcon: (filtered: boolean) => (
        <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
      ),
      filteredValue: filteredInfo.business || null,
      render: (business: string) => (
        <Tag color="blue">{business}</Tag>
      ),
    },
    {
      title: '业务系统英文名',
      dataIndex: 'business_en',
      key: 'business_en',
      width: 180,
      sorter: true,
      sortOrder: getSortOrder('business_en'),
      render: (businessEn: string) => (
        <span className="text-xs text-gray-600">{businessEn}</span>
      ),
    },
    {
      title: '主机名称',
      dataIndex: 'hostname',
      key: 'hostname',
      width: 180,
      sorter: true,
      sortOrder: getSortOrder('hostname'),
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: FilterDropdownProps) => (
        <div className="p-2">
          <Input
            placeholder="搜索主机名称"
            value={selectedKeys[0]}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => confirm()}
            className="w-48 mb-2 block"
          />
          <Space>
            <Button
              type="primary"
              onClick={() => confirm()}
              icon={<SearchOutlined />}
              size="small"
              className="w-20"
            >
              搜索
            </Button>
            <Button onClick={() => clearFilters && clearFilters()} size="small" className="w-20">
              重置
            </Button>
          </Space>
        </div>
      ),
      filterIcon: (filtered: boolean) => (
        <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
      ),
      filteredValue: filteredInfo.hostname || null,
      render: (hostname: string) => (
        <Tag color="green">{hostname}</Tag>
      ),
    },
    {
      title: '告警来源',
      dataIndex: 'location',
      key: 'location',
      width: 150,
      sorter: true,
      sortOrder: getSortOrder('location'),
      render: (location: string) => (
        <Tag color="orange">{location}</Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      key: 'create_time',
      width: 180,
      sorter: true,
      sortOrder: getSortOrder('create_time'),
      render: (time: string) => (
        <span className="text-xs text-gray-600">{time}</span>
      ),
    },
    {
      title: '更新时间',
      dataIndex: 'update_time',
      key: 'update_time',
      width: 180,
      sorter: true,
      sortOrder: getSortOrder('update_time'),
      render: (time: string) => (
        <span className="text-xs text-gray-600">{time}</span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 140,
      fixed: 'right',
      render: (_, record: OtherInfo) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => onEdit(record)}
            className={tableStyles.actionButton}
          >
            编辑
          </Button>
          <Popconfirm
            title="确认删除"
            description="确定要删除这个其他信息配置吗？"
            onConfirm={() => onDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
              className={tableStyles.actionButton}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];
};
