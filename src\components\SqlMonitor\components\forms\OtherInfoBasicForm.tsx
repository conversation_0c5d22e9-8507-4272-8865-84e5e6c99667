import React, { useEffect } from 'react';
import { Form, Input, Card, Row, Col, Button } from 'antd';
import type { FormInstance } from 'antd';

import type { OtherInfo } from '../../types';
import { formStyles } from '../../styles';
import { FORM_BUTTON_TEXT } from '../../constants';

interface OtherInfoBasicFormProps {
  form: FormInstance;
  initialData?: OtherInfo;
  onSubmit?: (values: any) => void;
  onCancel?: () => void;
  onReset?: () => void;
  loading?: boolean;
}

/**
 * 其他信息基本信息表单组件
 * 用于新增和编辑其他信息配置
 */
const OtherInfoBasicForm: React.FC<OtherInfoBasicFormProps> = ({
  form,
  initialData,
  onSubmit,
  onCancel,
  onReset,
  loading = false,
}) => {
  // 初始化表单数据
  useEffect(() => {
    if (initialData) {
      form.setFieldsValue(initialData);
    }
  }, [form, initialData]);

  // 表单提交处理
  const handleSubmit = (values: any) => {
    console.log('其他信息表单提交:', values);
    onSubmit?.(values);
  };

  // 重置表单
  const handleReset = () => {
    form.resetFields();
    onReset?.();
  };

  return (
    <div className={formStyles.container}>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        className={formStyles.form}
      >
        <Card title="基本信息" className={formStyles.card}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="信息名称"
                rules={[
                  { required: true, message: '请输入信息名称' },
                  { max: 50, message: '信息名称不能超过50个字符' },
                ]}
              >
                <Input placeholder="请输入信息名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="business"
                label="业务系统名称"
                rules={[
                  { required: true, message: '请输入业务系统名称' },
                  { max: 100, message: '业务系统名称不能超过100个字符' },
                ]}
              >
                <Input placeholder="请输入业务系统名称" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="business_en"
                label="业务系统英文名称"
                rules={[
                  { required: true, message: '请输入业务系统英文名称' },
                  { max: 100, message: '业务系统英文名称不能超过100个字符' },
                  { 
                    pattern: /^[a-zA-Z0-9_-]+$/,
                    message: '英文名称只能包含字母、数字、下划线和连字符'
                  },
                ]}
              >
                <Input placeholder="请输入业务系统英文名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="hostname"
                label="主机名称"
                rules={[
                  { required: true, message: '请输入主机名称' },
                  { max: 100, message: '主机名称不能超过100个字符' },
                ]}
              >
                <Input placeholder="请输入主机名称" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="location"
                label="告警来源"
                rules={[
                  { required: true, message: '请输入告警来源' },
                  { max: 200, message: '告警来源不能超过200个字符' },
                ]}
              >
                <Input placeholder="请输入告警来源" />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 操作按钮 */}
        <div className={formStyles.buttonGroup}>
          <Button type="primary" htmlType="submit" loading={loading}>
            {FORM_BUTTON_TEXT.submit}
          </Button>
          <Button onClick={handleReset}>
            {FORM_BUTTON_TEXT.reset}
          </Button>
          <Button onClick={onCancel}>
            {FORM_BUTTON_TEXT.cancel}
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default OtherInfoBasicForm;
