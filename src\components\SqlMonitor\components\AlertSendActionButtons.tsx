import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import React from 'react';

import { tableStyles } from '../styles';

interface AlertSendActionButtonsProps {
  selectedCount: number;
  onAddAlertSend: () => void;
  onBatchDelete: () => void;
  onClearSelection: () => void;
}

/**
 * 告警发送操作按钮组件
 * 包含新增、批量删除等操作按钮
 */
export const AlertSendActionButtons: React.FC<AlertSendActionButtonsProps> = ({
  selectedCount,
  onAddAlertSend,
  onBatchDelete,
  onClearSelection,
}) => {
  return (
    <>
      {/* 常规操作按钮区域 */}
      <div className={tableStyles.actionButtonsContainer}>
        <div className={tableStyles.actionButtonsLeft}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={onAddAlertSend}
            className={tableStyles.primaryButton}
          >
            新增告警发送
          </Button>
        </div>
      </div>

      {/* 批量操作栏 - 当有选中项时显示 */}
      {selectedCount > 0 && (
        <div className={tableStyles.batchActionBar}>
          <div className={tableStyles.batchActionContent}>
            <span className={tableStyles.batchActionText}>
              已选择 <strong>{selectedCount}</strong> 项
            </span>
            <div className={tableStyles.batchActionButtons}>
              <Button
                danger
                icon={<DeleteOutlined />}
                onClick={onBatchDelete}
                className={tableStyles.batchDeleteButton}
              >
                批量删除
              </Button>
              <Button onClick={onClearSelection} className={tableStyles.clearSelectionButton}>
                取消选择
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
